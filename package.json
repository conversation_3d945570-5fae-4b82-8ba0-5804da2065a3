{"name": "animezera", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "vercel-build": "prisma generate && prisma db push --accept-data-loss && ts-node scripts/update-slugs.ts && next build", "update-slugs": "ts-node scripts/update-slugs.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.9.0", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@prisma/client": "^6.7.0", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/uuid": "^10.0.0", "@vercel/analytics": "^1.5.0", "@vitalets/google-translate-api": "^9.2.1", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cheerio": "^1.0.0", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "fluent-ffmpeg": "^2.1.3", "framer-motion": "^12.9.4", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.503.0", "next": "15.3.1", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "node-fetch": "^2.7.0", "nodemailer": "^6.10.1", "pg": "^8.15.6", "plyr": "^3.7.8", "prisma": "^6.7.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "slugify": "^1.6.6", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0", "video.js": "^8.22.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/postcss": "^4", "@types/fluent-ffmpeg": "^2.1.27", "@types/node": "^20", "@types/node-fetch": "^2.6.12", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}}