'use client'

import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import Episode<PERSON>arousel from '@/components/EpisodeCarousel'
import AdBanner from '@/components/AdBanner'

interface TrendingAnime {
  id: string
  title: string
  image: string
  description: string
  status: string
  totalEpisodes: number
  studio: string
  year: number
  genres: string[]
  slug: string
  audio: string
  likes: number
}

export default function Home() {
  const [trendingAnimes, setTrendingAnimes] = useState<TrendingAnime[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchTrendingAnimes()
  }, [])

  const fetchTrendingAnimes = async () => {
    try {
      const response = await fetch('/api/animes/trending')
      if (!response.ok) {
        throw new Error('Failed to fetch trending animes')
      }
      const data = await response.json()
      setTrendingAnimes(data)
    } catch (error) {
      console.error('Error fetching trending animes:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <main className="min-h-screen bg-gray-900">
      {/* Banner Topo */}
      <div className="container mx-auto px-4 pt-4">
        <AdBanner position="top" />
      </div>

      {/* Hero Section */}
      <section className="relative h-[600px] flex items-center">
        <div className="absolute inset-0">
          <Image
            src="/anime-hero-bg.png"
            alt="Hero Background"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-gray-900 via-gray-900/80 to-transparent" />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-2xl">
            <h1 className="text-5xl font-bold text-white mb-6">
              Bem-vindo ao <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400">AnimesZera</span>
            </h1>
            <p className="text-xl text-gray-300 mb-8">
              Assista seus animes favoritos em alta qualidade, legendado e dublado.
            </p>

            <div className="flex flex-wrap gap-6">
              <Link
                href="/animes"
                className="group relative px-8 py-4 text-lg font-semibold text-white rounded-lg overflow-hidden transition-all duration-300 hover:scale-105"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 group-hover:from-purple-700 group-hover:to-blue-700 transition-colors duration-300"></div>
                <span className="relative flex items-center gap-2">
                  Explorar Animes
                  <svg className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </span>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Banner In-Content após Hero */}
      <div className="container mx-auto px-4 py-6">
        <AdBanner position="in-content" />
      </div>

      {/* Episode Carousel */}
      <section className="container mx-auto px-4">
        <EpisodeCarousel />
      </section>

      {/* Trending Animes Section */}
      <section className="container mx-auto px-4 py-12">
        <div className="flex items-center justify-between mb-12">
          <h2 className="text-4xl font-bold text-white">
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400">Animes</span> em Destaque
          </h2>
        </div>
        {isLoading ? (
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-8">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="aspect-[2/3] bg-gray-800 rounded-xl mb-4" />
                <div className="h-6 bg-gray-800 rounded w-3/4 mb-2" />
                <div className="h-4 bg-gray-800 rounded w-1/2" />
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-8">
            {trendingAnimes.map((anime) => (
              <Link
                key={anime.id}
                href={`/animes/${anime.slug}`}
                className="group"
              >
                <div className="relative aspect-[2/3] rounded-xl overflow-hidden mb-4 transform transition-transform duration-300 group-hover:scale-105">
                  <Image
                    src={anime.image}
                    alt={anime.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <div className="absolute bottom-0 left-0 right-0 p-6 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                    <p className="text-white text-sm line-clamp-3">
                      {anime.description}
                    </p>
                  </div>
                  <div className="absolute top-2 right-2 bg-purple-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                    {anime.likes} likes
                  </div>
                  <div className="absolute top-2 left-2">
                    <span className={`text-xs font-medium px-2 py-1 rounded-md ${anime.audio === 'Dublado' ? 'bg-blue-600' : 'bg-yellow-600'} text-white`}>
                      {anime.audio || 'Legendado'}
                    </span>
                  </div>
                </div>
                <h3 className="text-white font-semibold text-lg mb-2 group-hover:text-purple-400 transition-colors duration-300">
                  {anime.title}
                </h3>
                <div className="flex items-center text-gray-400 text-sm">
                  <span>{anime.year}</span>
                  <span className="mx-2">•</span>
                  <span>{anime.totalEpisodes} Episódios</span>
                  <span className="mx-2">•</span>
                  <span className={anime.audio === 'Dublado' ? 'text-blue-400' : 'text-yellow-400'}>
                    {anime.audio || 'Legendado'}
                  </span>
                </div>
              </Link>
            ))}
          </div>
        )}
      </section>

      {/* Banner Rodapé */}
      <div className="container mx-auto px-4 pb-8">
        <AdBanner position="bottom" />
      </div>
    </main>
  );
}
