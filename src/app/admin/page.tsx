'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { toast } from 'react-hot-toast'

interface Stats {
  totalAnimes: number
  totalEpisodes: number
  totalUsers: number
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<Stats>({
    totalAnimes: 0,
    totalEpisodes: 0,
    totalUsers: 0,
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setIsLoading(true)
        console.log('Iniciando busca de estatísticas...')

        const response = await fetch('/api/admin/stats')
        console.log('Resposta recebida:', {
          status: response.status,
          statusText: response.statusText,
          ok: response.ok
        })

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          console.error('Detalhes do erro:', errorData)
          throw new Error(`Falha ao carregar estatísticas: ${response.status} ${response.statusText}`)
        }

        const data = await response.json()
        console.log('Dados recebidos:', data)

        // Verificar se os dados estão no formato esperado
        if (typeof data.totalAnimes !== 'number' ||
            typeof data.totalEpisodes !== 'number' ||
            typeof data.totalUsers !== 'number') {
          console.error('Formato de dados inválido:', data)
          throw new Error('Formato de dados inválido')
        }

        setStats(data)
        console.log('Estatísticas atualizadas com sucesso')
      } catch (error) {
        console.error('Erro ao carregar estatísticas:', error)
        toast.error(error instanceof Error ? error.message : 'Erro ao carregar estatísticas')
      } finally {
        setIsLoading(false)
      }
    }

    fetchStats()
  }, [])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0">
        <h1 className="text-2xl font-bold">Dashboard</h1>
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 w-full sm:w-auto">
          <Link
            href="/admin/animes"
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-center"
          >
            Gerenciar Animes
          </Link>
          <Link
            href="/admin/schedule-status"
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-center"
          >
            Verificar Episódios
          </Link>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Total de Animes</p>
              {isLoading ? (
                <div className="animate-pulse h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded mt-1"></div>
              ) : (
                <p className="text-2xl font-bold">{stats.totalAnimes?.toLocaleString() || '0'}</p>
              )}
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Total de Episódios</p>
              {isLoading ? (
                <div className="animate-pulse h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded mt-1"></div>
              ) : (
                <p className="text-2xl font-bold">{stats.totalEpisodes?.toLocaleString() || '0'}</p>
              )}
            </div>
            <div className="p-3 bg-green-100 dark:bg-green-900 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Total de Usuários</p>
              {isLoading ? (
                <div className="animate-pulse h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded mt-1"></div>
              ) : (
                <p className="text-2xl font-bold">{stats.totalUsers?.toLocaleString() || '0'}</p>
              )}
            </div>
            <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Dashboard Info */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4">Bem-vindo ao Painel Administrativo</h2>
        <p className="text-gray-600 dark:text-gray-300 mb-4">
          Este é o painel administrativo do AnimesZera. Aqui você pode gerenciar animes, episódios e verificar estatísticas do site.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h3 className="font-medium text-blue-700 dark:text-blue-300 mb-2">Gerenciar Animes</h3>
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
              Adicione, edite ou remova animes do catálogo. Você também pode importar animes completos do AnimeFire.
            </p>
            <Link
              href="/admin/animes"
              className="inline-flex items-center text-sm font-medium text-blue-600 dark:text-blue-400 hover:underline"
            >
              Ir para Gerenciamento de Animes
              <svg className="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd"></path>
              </svg>
            </Link>
          </div>

          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <h3 className="font-medium text-green-700 dark:text-green-300 mb-2">Verificar Novos Episódios</h3>
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
              Verifique e adicione automaticamente novos episódios para animes em lançamento.
            </p>
            <Link
              href="/admin/schedule-status"
              className="inline-flex items-center text-sm font-medium text-green-600 dark:text-green-400 hover:underline"
            >
              Ir para Status do Agendamento
              <svg className="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd"></path>
              </svg>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}