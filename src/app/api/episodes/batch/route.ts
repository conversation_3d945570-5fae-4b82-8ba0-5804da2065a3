import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

interface EpisodeData {
  number: number
  videoUrl: string
  sourceType: string
}

export async function POST(request: Request) {
  try {
    // Verificar autenticação
    const session = await getServerSession(authOptions)
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      )
    }

    // Obter dados do corpo da requisição
    const body = await request.json()
    const { animeId, episodes } = body

    if (!animeId) {
      return NextResponse.json(
        { error: 'ID do anime é obrigatório' },
        { status: 400 }
      )
    }

    if (!episodes || !Array.isArray(episodes) || episodes.length === 0) {
      return NextResponse.json(
        { error: 'Lista de episódios é obrigatória' },
        { status: 400 }
      )
    }

    // Verificar se o anime existe
    const anime = await prisma.anime.findUnique({
      where: { id: animeId },
      include: { episodes: true }
    })

    if (!anime) {
      return NextResponse.json(
        { error: 'Anime não encontrado' },
        { status: 404 }
      )
    }

    // Processar cada episódio
    const results = []
    const existingEpisodeNumbers = anime.episodes.map(ep => ep.number)

    for (const episodeData of episodes) {
      const { number, videoUrl, sourceType } = episodeData

      // Verificar se o episódio já existe
      if (existingEpisodeNumbers.includes(number)) {
        // Atualizar episódio existente
        const updatedEpisode = await prisma.episode.updateMany({
          where: {
            animeId,
            number
          },
          data: {
            videoUrl,
            sourceType: sourceType || 'unknown',
            updatedAt: new Date()
          }
        })

        results.push({
          number,
          action: 'updated',
          success: true
        })
      } else {
        // Criar novo episódio
        const newEpisode = await prisma.episode.create({
          data: {
            animeId,
            number,
            videoUrl,
            sourceType: sourceType || 'unknown',
            airDate: new Date()
          }
        })

        results.push({
          number,
          action: 'created',
          success: true
        })
      }
    }

    return NextResponse.json({
      message: `Processados ${results.length} episódios com sucesso`,
      results
    })
  } catch (error) {
    console.error('Erro ao processar episódios em lote:', error)
    return NextResponse.json(
      { error: 'Erro ao processar episódios' },
      { status: 500 }
    )
  }
}
