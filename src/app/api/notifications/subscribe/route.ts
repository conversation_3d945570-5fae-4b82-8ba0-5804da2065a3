import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json(
      { error: 'Não autorizado' },
      { status: 401 }
    )
  }

  try {
    const { subscription, animeId } = await request.json()

    if (!subscription || !animeId) {
      return NextResponse.json(
        { error: 'Dados de inscrição incompletos' },
        { status: 400 }
      )
    }

    // Verificar se o anime existe
    const anime = await prisma.anime.findUnique({
      where: { id: animeId }
    })

    if (!anime) {
      return NextResponse.json(
        { error: 'Anime não encontrado' },
        { status: 404 }
      )
    }

    // Salvar ou atualizar a inscrição push do usuário
    await prisma.pushSubscription.upsert({
      where: {
        userId: session.user.id
      },
      update: {
        endpoint: subscription.endpoint,
        p256dh: subscription.keys.p256dh,
        auth: subscription.keys.auth,
        expirationTime: subscription.expirationTime?.toString() || null
      },
      create: {
        userId: session.user.id,
        endpoint: subscription.endpoint,
        p256dh: subscription.keys.p256dh,
        auth: subscription.keys.auth,
        expirationTime: subscription.expirationTime?.toString() || null
      }
    })

    // Criar ou atualizar a inscrição do anime
    await prisma.animeSubscription.upsert({
      where: {
        userId_animeId: {
          userId: session.user.id,
          animeId
        }
      },
      update: {
        updatedAt: new Date()
      },
      create: {
        userId: session.user.id,
        animeId
      }
    })

    return NextResponse.json({
      success: true,
      message: `Inscrição para notificações de ${anime.title} realizada com sucesso`
    })
  } catch (error) {
    console.error('Erro ao processar inscrição:', error)
    return NextResponse.json(
      { error: 'Erro ao processar inscrição' },
      { status: 500 }
    )
  }
}
