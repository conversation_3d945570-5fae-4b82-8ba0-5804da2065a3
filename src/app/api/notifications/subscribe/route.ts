import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  console.log('API subscribe chamada');

  try {
    const session = await getServerSession(authOptions)
    console.log('Sessão obtida:', session ? 'Sim' : 'Não');

    if (!session?.user) {
      console.log('Usuário não autenticado');
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      )
    }

    const body = await request.json()
    console.log('Corpo da requisição recebido');

    const { subscription, animeId } = body
    console.log('AnimeId recebido:', animeId);
    console.log('Subscription recebida:', subscription ? 'Sim' : 'Não');

    if (!subscription || !animeId) {
      console.log('Dados incompletos:', { subscription: !!subscription, animeId: !!animeId });
      return NextResponse.json(
        { error: 'Dados de inscrição incompletos' },
        { status: 400 }
      )
    }

    // Verificar se o anime existe
    console.log('Verificando se o anime existe...');
    const anime = await prisma.anime.findUnique({
      where: { id: animeId }
    })
    console.log('Anime encontrado:', anime ? 'Sim' : 'Não');

    if (!anime) {
      console.log('Anime não encontrado com ID:', animeId);
      return NextResponse.json(
        { error: 'Anime não encontrado' },
        { status: 404 }
      )
    }

    // Salvar ou atualizar a inscrição push do usuário
    console.log('Salvando dados de push subscription...');
    try {
      await prisma.pushSubscription.upsert({
        where: {
          userId: session.user.id
        },
        update: {
          endpoint: subscription.endpoint,
          p256dh: subscription.keys.p256dh,
          auth: subscription.keys.auth,
          expirationTime: subscription.expirationTime?.toString() || null
        },
        create: {
          userId: session.user.id,
          endpoint: subscription.endpoint,
          p256dh: subscription.keys.p256dh,
          auth: subscription.keys.auth,
          expirationTime: subscription.expirationTime?.toString() || null
        }
      })
      console.log('Push subscription salva com sucesso');
    } catch (error) {
      console.error('Erro ao salvar push subscription:', error);
      throw error;
    }

    // Criar ou atualizar a inscrição do anime
    console.log('Salvando inscrição do anime...');
    try {
      await prisma.animeSubscription.upsert({
        where: {
          userId_animeId: {
            userId: session.user.id,
            animeId
          }
        },
        update: {
          updatedAt: new Date()
        },
        create: {
          userId: session.user.id,
          animeId
        }
      })
      console.log('Inscrição do anime salva com sucesso');
    } catch (error) {
      console.error('Erro ao salvar inscrição do anime:', error);
      throw error;
    }

    console.log('Inscrição concluída com sucesso');
    return NextResponse.json({
      success: true,
      message: `Inscrição para notificações de ${anime.title} realizada com sucesso`
    })
  } catch (error) {
    console.error('Erro ao processar inscrição:', error)
    return NextResponse.json(
      { error: 'Erro ao processar inscrição', details: error instanceof Error ? error.message : 'Erro desconhecido' },
      { status: 500 }
    )
  }
}
