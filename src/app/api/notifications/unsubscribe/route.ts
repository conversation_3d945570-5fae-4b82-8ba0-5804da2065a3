import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json(
      { error: 'Não autorizado' },
      { status: 401 }
    )
  }

  try {
    const { animeId } = await request.json()

    if (!animeId) {
      return NextResponse.json(
        { error: 'ID do anime não fornecido' },
        { status: 400 }
      )
    }

    // Verificar se o anime existe
    const anime = await prisma.anime.findUnique({
      where: { id: animeId }
    })

    if (!anime) {
      return NextResponse.json(
        { error: 'Anime não encontrado' },
        { status: 404 }
      )
    }

    // Remover a inscrição do anime
    await prisma.animeSubscription.deleteMany({
      where: {
        userId: session.user.id,
        animeId
      }
    })

    // Verificar se o usuário ainda tem outras inscrições
    const remainingSubscriptions = await prisma.animeSubscription.count({
      where: {
        userId: session.user.id
      }
    })

    // Se não houver mais inscrições, remover também a PushSubscription
    if (remainingSubscriptions === 0) {
      await prisma.pushSubscription.deleteMany({
        where: {
          userId: session.user.id
        }
      })
    }

    return NextResponse.json({
      success: true,
      message: `Inscrição para notificações de ${anime.title} cancelada com sucesso`
    })
  } catch (error) {
    console.error('Erro ao cancelar inscrição:', error)
    return NextResponse.json(
      { error: 'Erro ao cancelar inscrição' },
      { status: 500 }
    )
  }
}
