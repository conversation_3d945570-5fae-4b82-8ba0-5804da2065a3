import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json(
      { error: 'Não autorizado' },
      { status: 401 }
    )
  }

  const searchParams = request.nextUrl.searchParams
  const animeId = searchParams.get('animeId')

  if (!animeId) {
    return NextResponse.json(
      { error: 'ID do anime não fornecido' },
      { status: 400 }
    )
  }

  try {
    // Verificar se o usuário está inscrito neste anime
    const subscription = await prisma.animeSubscription.findUnique({
      where: {
        userId_animeId: {
          userId: session.user.id,
          animeId
        }
      }
    })

    return NextResponse.json({
      isSubscribed: !!subscription
    })
  } catch (error) {
    console.error('Erro ao verificar status da inscrição:', error)
    return NextResponse.json(
      { error: 'Erro ao verificar status da inscrição' },
      { status: 500 }
    )
  }
}
