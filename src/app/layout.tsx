import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/ThemeProvider";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Providers from "@/components/Providers";
import LogObfuscatorInitializer from "@/components/LogObfuscatorInitializer";
import InstallPWABanner from "@/components/InstallPWABanner";
import RegisterSW from "@/components/RegisterSW";
import { Analytics } from "@vercel/analytics/next";
import { MontagAds } from '@/components/MontagAds'

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Animes Zera - Assista Animes Online em HD",
  description: "A maior coleção de animes online. Assista seus animes favoritos em qualidade HD, com legendas em português e atualizações diárias.",
  manifest: '/manifest.json',
  themeColor: '#111827',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'black-translucent',
    title: 'AnimesZera',
  },
  icons: {
    icon: '/favicon.svg',
    shortcut: '/favicon.svg',
    apple: '/favicon.svg',
  },
  // Meta tag de verificação
  other: {
    'ee33054734073d4f89cfb58f3c38e70e6ce1a00e': 'ee33054734073d4f89cfb58f3c38e70e6ce1a00e',
    'referrer': 'no-referrer-when-downgrade',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="pt-BR" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
            <div className="min-h-screen bg-gray-900">
              <Navbar />
              <main className="container mx-auto px-4 py-8">
                {children}
              </main>
              <Footer />
              <InstallPWABanner />
            </div>
          </ThemeProvider>
          {/* Inicializa o obfuscador de logs para ocultar referências sensíveis */}
          <LogObfuscatorInitializer />
          <RegisterSW />

          {/* Vercel Analytics */}
          <Analytics />

          {/* Add Monetag Ads - Popunder removido para melhorar a experiência do usuário */}
          <MontagAds type="vignette" />
          <MontagAds type="directLink" />
        </Providers>
      </body>
    </html>
  );
}
