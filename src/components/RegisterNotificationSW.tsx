'use client'

import { useEffect } from 'react'

export default function RegisterNotificationSW() {
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/notification-sw.js')
          .then(registration => {
            console.log('Notification Service Worker registrado com sucesso:', registration.scope)
          })
          .catch(error => {
            console.log('Falha ao registrar o Notification Service Worker:', error)
          })
      })
    }
  }, [])

  return null
}
