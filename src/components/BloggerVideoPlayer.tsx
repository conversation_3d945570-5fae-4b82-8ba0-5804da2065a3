'use client'

import { useRef, useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'

interface BloggerVideoPlayerProps {
  videoUrl: string
  title?: string
  className?: string
  episodeId?: string
  animeSlug?: string
  onNextEpisode?: () => void
}

export default function BloggerVideoPlayer({
  videoUrl,
  title = 'Episódio de Anime',
  className = '',
  episodeId,
  animeSlug,
  onNextEpisode
}: BloggerVideoPlayerProps) {
  const { data: session } = useSession()
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [lastSaveTime, setLastSaveTime] = useState(0)
  const saveInterval = 30000 // Salvar a cada 30 segundos para vídeos do Blogger
  const [hasMarkedAsWatched, setHasMarkedAsWatched] = useState(false)

  // Iniciar o timer para salvar o progresso e marcar como assistido
  useEffect(() => {
    if (!episodeId || !session?.user) return

    // Marcar como assistido após um tempo
    const watchTimer = setTimeout(() => {
      if (!hasMarkedAsWatched && animeSlug && episodeId) {
        console.log('Marcando episódio do Blogger como assistido:', episodeId)
        setHasMarkedAsWatched(true)
        updateWatchedStatus(episodeId, true)

        // Salvar progresso como 90% completo para episódios do Blogger
        saveWatchProgress(900, 1000) // Valores arbitrários para representar 90% completo
      }
    }, 60000) // Marcar como assistido após 1 minuto de visualização

    // Salvar progresso periodicamente
    const progressInterval = setInterval(() => {
      if (episodeId) {
        console.log('Salvando progresso para episódio do Blogger:', episodeId)
        // Para Blogger, usamos valores arbitrários para indicar progresso
        // Começamos com 10% e aumentamos gradualmente até 90%
        const now = Date.now()
        const elapsedTime = now - lastSaveTime
        if (elapsedTime >= saveInterval) {
          setLastSaveTime(now)

          // Calcular um valor de progresso baseado no tempo decorrido
          // Máximo de 90% para não marcar como totalmente assistido automaticamente
          const progressPercentage = Math.min(0.9, elapsedTime / (10 * 60 * 1000)) // Máximo de 10 minutos
          saveWatchProgress(progressPercentage * 1000, 1000)
        }
      }
    }, saveInterval)

    // Salvar no localStorage que este episódio está sendo assistido
    try {
      if (episodeId) {
        localStorage.setItem(`blogger_watching_${episodeId}`, 'true')
        localStorage.setItem(`video_progress_${episodeId}`, '100') // Valor arbitrário para aparecer no continue assistindo
      }
    } catch (e) {
      console.error('Erro ao salvar no localStorage:', e)
    }

    return () => {
      clearTimeout(watchTimer)
      clearInterval(progressInterval)
    }
  }, [episodeId, animeSlug, session, hasMarkedAsWatched, lastSaveTime])

  // Salvar o progresso quando o usuário sai da página
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (episodeId && !hasMarkedAsWatched) {
        // Marcar como assistido ao sair da página se já passou tempo suficiente
        const watchingTime = Date.now() - lastSaveTime
        if (watchingTime > 30000) { // Se assistiu por mais de 30 segundos
          updateWatchedStatus(episodeId, true)
          saveWatchProgress(900, 1000) // 90% completo
        }
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [episodeId, hasMarkedAsWatched, lastSaveTime])

  // Salvar o progresso de visualização
  const saveWatchProgress = async (currentTime: number, duration: number) => {
    if (!episodeId) return

    // Sempre salvar no localStorage, mesmo sem sessão
    try {
      localStorage.setItem(`video_progress_${episodeId}`, String(currentTime))
      localStorage.setItem(`blogger_video_${episodeId}`, 'true')
      console.log('Progresso do Blogger salvo no localStorage:', currentTime)
    } catch (e) {
      console.error('Erro ao salvar progresso do Blogger no localStorage:', e)
    }

    // Salvar no servidor apenas se o usuário estiver logado
    if (!session?.user) return

    try {
      await fetch('/api/watch-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          episodeId,
          animeSlug, // Importante incluir o animeSlug
          currentTime,
          duration,
          isBloggerEpisode: true // Marcar explicitamente como episódio do Blogger
        }),
      })
      console.log('Progresso do Blogger salvo no servidor:', currentTime)
    } catch (error) {
      console.error('Erro ao salvar progresso do Blogger no servidor:', error)
    }
  }

  // Atualizar o status de assistido
  const updateWatchedStatus = async (episodeId: string, watched: boolean) => {
    if (!animeSlug || !session?.user) return

    try {
      const response = await fetch(`/api/animes/${animeSlug}/watched-episodes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          episodeId,
          watched,
        }),
      })

      if (!response.ok) {
        throw new Error('Falha ao atualizar status de assistido')
      }

      console.log('Episódio do Blogger marcado como assistido:', episodeId)
    } catch (error) {
      console.error('Erro ao atualizar status de episódio do Blogger:', error)
    }
  }

  return (
    <div className={`relative aspect-video bg-black ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/80 z-10 pointer-events-none">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
        </div>
      )}

      <iframe
        ref={iframeRef}
        src={videoUrl}
        className="w-full h-full"
        allowFullScreen
        allow="fullscreen; accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        frameBorder="0"
        scrolling="no"
        title={title}
        onLoad={() => {
          setIsLoading(false)
          setLastSaveTime(Date.now()) // Iniciar o contador quando o iframe carregar

          // Adicionar classe para garantir que o iframe ocupe todo o espaço disponível
          if (iframeRef.current) {
            iframeRef.current.style.width = '100%'
            iframeRef.current.style.height = '100%'
            iframeRef.current.style.position = 'absolute'
            iframeRef.current.style.top = '0'
            iframeRef.current.style.left = '0'
          }
        }}
      />

      {/* Botão de play para garantir interação em dispositivos móveis */}
      <div
        className="absolute inset-0 flex items-center justify-center cursor-pointer z-10"
        onClick={() => {
          // Em dispositivos móveis, tentar entrar em tela cheia ao clicar no player
          if (window.innerWidth < 768 && iframeRef.current) {
            try {
              // Tentar solicitar tela cheia para o iframe
              const requestFullscreen = iframeRef.current.requestFullscreen ||
                (iframeRef.current as any).webkitRequestFullscreen ||
                (iframeRef.current as any).mozRequestFullScreen ||
                (iframeRef.current as any).msRequestFullscreen

              if (requestFullscreen) {
                requestFullscreen.call(iframeRef.current)
              }
            } catch (error) {
              console.error('Erro ao tentar entrar em tela cheia:', error)
            }
          }
        }}
        style={{ display: isLoading ? 'none' : 'flex' }}
      >
        {/* Transparente - apenas para capturar cliques */}
      </div>
    </div>
  )
}
