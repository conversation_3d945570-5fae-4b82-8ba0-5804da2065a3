'use client'

import { useRef, useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'

interface CustomVideoPlayerProps {
  videoUrl: string
  poster?: string
  title?: string
  className?: string
  onNextEpisode?: () => void
  episodeId?: string
  animeSlug?: string
}

export default function CustomVideoPlayer({
  videoUrl,
  poster,
  title = 'Episódio de Anime',
  className = '',
  onNextEpisode,
  episodeId,
  animeSlug
}: CustomVideoPlayerProps) {
  const { data: session } = useSession()
  const videoRef = useRef<HTMLVideoElement | null>(null)
  const progressRef = useRef<HTMLDivElement | null>(null)
  const volumeRef = useRef<HTMLDivElement | null>(null)
  const containerRef = useRef<HTMLDivElement | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [showControls, setShowControls] = useState(true)
  const [hasMarkedAsWatched, setHasMarkedAsWatched] = useState(false)
  const [lastSaveTime, setLastSaveTime] = useState(0)
  const saveInterval = 5000 // Salvar a cada 5 segundos para maior frequência
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Carregar o progresso assim que o componente for montado
  useEffect(() => {
    if (videoRef.current && episodeId) {
      // Primeiro, verificar se há progresso salvo no localStorage
      try {
        const savedProgress = localStorage.getItem(`video_progress_${episodeId}`)
        if (savedProgress) {
          const savedTime = parseFloat(savedProgress)
          console.log('Progresso encontrado no localStorage:', savedTime)

          // Aplicar o progresso do localStorage imediatamente
          if (savedTime > 0 && videoRef.current) {
            console.log('Aplicando progresso do localStorage:', savedTime)
            videoRef.current.currentTime = savedTime
            setCurrentTime(savedTime)
          }
        }
      } catch (e) {
        console.error('Erro ao recuperar progresso do localStorage:', e)
      }

      // Depois, carregar o progresso do servidor
      loadWatchProgress()

      // Tentar iniciar a reprodução automaticamente após um pequeno atraso
      const autoplayTimer = setTimeout(() => {
        if (videoRef.current) {
          videoRef.current.play()
            .then(() => {
              console.log('Reprodução automática iniciada com sucesso')
              setIsPlaying(true)
            })
            .catch(err => {
              console.log('Reprodução automática falhou, aguardando interação do usuário', err)
            })
        }
      }, 1000)

      return () => clearTimeout(autoplayTimer)
    }
  }, [episodeId])

  // Configurar o player de vídeo
  useEffect(() => {
    if (!videoRef.current) return

    const videoElement = videoRef.current

    // Eventos do player
    const handleLoadedData = () => {
      // Forçar a remoção do estado de carregamento
      setIsLoading(false)
      setDuration(videoElement.duration)
      // Não precisamos chamar loadWatchProgress aqui, pois já chamamos no useEffect acima
    }

    // Evento adicional para garantir que o loading seja removido
    const handleCanPlay = () => {
      // Forçar a remoção do estado de carregamento
      setIsLoading(false)
    }

    // Evento adicional para quando o vídeo começa a reproduzir
    const handlePlaying = () => {
      // Forçar a remoção do estado de carregamento
      setIsLoading(false)
    }

    // Evento adicional para qualquer interação com o vídeo
    const handleUserInteraction = () => {
      // Forçar a remoção do estado de carregamento
      setIsLoading(false)
    }

    const handleTimeUpdate = () => {
      setCurrentTime(videoElement.currentTime)

      // Marcar como assistido quando atingir 80% do vídeo
      if (!hasMarkedAsWatched && videoElement.currentTime > videoElement.duration * 0.8 && animeSlug && episodeId) {
        setHasMarkedAsWatched(true)
        updateWatchedStatus(episodeId, true)
      }

      // Salvar o progresso a cada intervalo
      const now = Date.now()
      if (now - lastSaveTime >= saveInterval) {
        setLastSaveTime(now)
        saveWatchProgress(videoElement.currentTime, videoElement.duration)
      }
    }

    const handlePlay = () => {
      setIsPlaying(true)
    }

    const handlePause = () => {
      setIsPlaying(false)
    }

    const handleVolumeChange = () => {
      setVolume(videoElement.volume)
      setIsMuted(videoElement.muted)
    }

    const handleEnded = () => {
      setIsPlaying(false)
      if (onNextEpisode) {
        console.log('Vídeo terminou, indo para o próximo episódio...')
        setTimeout(() => {
          onNextEpisode()
        }, 1000)
      }
    }

    const handleError = () => {
      setIsLoading(false)
      console.error('Erro ao carregar o vídeo')
    }

    // Adicionar event listeners
    videoElement.addEventListener('loadeddata', handleLoadedData)
    videoElement.addEventListener('canplay', handleCanPlay)
    videoElement.addEventListener('playing', handlePlaying)
    videoElement.addEventListener('timeupdate', handleTimeUpdate)
    videoElement.addEventListener('play', handlePlay)
    videoElement.addEventListener('pause', handlePause)
    videoElement.addEventListener('volumechange', handleVolumeChange)
    videoElement.addEventListener('ended', handleEnded)
    videoElement.addEventListener('error', handleError)
    videoElement.addEventListener('click', handleUserInteraction)
    videoElement.addEventListener('touchstart', handleUserInteraction)

    // Forçar a remoção do loading imediatamente e após um tempo limite (1 segundo)
    setIsLoading(false) // Forçar imediatamente

    const loadingTimeout = setTimeout(() => {
      setIsLoading(false)
    }, 1000)

    // Limpar event listeners quando o componente for desmontado
    return () => {
      clearTimeout(loadingTimeout)
      videoElement.removeEventListener('loadeddata', handleLoadedData)
      videoElement.removeEventListener('canplay', handleCanPlay)
      videoElement.removeEventListener('playing', handlePlaying)
      videoElement.removeEventListener('timeupdate', handleTimeUpdate)
      videoElement.removeEventListener('play', handlePlay)
      videoElement.removeEventListener('pause', handlePause)
      videoElement.removeEventListener('volumechange', handleVolumeChange)
      videoElement.removeEventListener('ended', handleEnded)
      videoElement.removeEventListener('error', handleError)
      videoElement.removeEventListener('click', handleUserInteraction)
      videoElement.removeEventListener('touchstart', handleUserInteraction)
    }
  }, [videoRef.current, hasMarkedAsWatched, animeSlug, episodeId, onNextEpisode, lastSaveTime])

  // Controlar a exibição dos controles
  useEffect(() => {
    const handleMouseMove = () => {
      setShowControls(true)

      // Limpar o timeout anterior
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current)
      }

      // Configurar um novo timeout para esconder os controles
      controlsTimeoutRef.current = setTimeout(() => {
        if (isPlaying) {
          setShowControls(false)
        }
      }, 3000)
    }

    const container = containerRef.current
    if (container) {
      container.addEventListener('mousemove', handleMouseMove)
      container.addEventListener('mouseenter', handleMouseMove)
      container.addEventListener('mouseleave', () => {
        if (isPlaying) {
          setShowControls(false)
        }
      })
    }

    return () => {
      if (container) {
        container.removeEventListener('mousemove', handleMouseMove)
        container.removeEventListener('mouseenter', handleMouseMove)
        container.removeEventListener('mouseleave', () => {})
      }

      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current)
      }
    }
  }, [isPlaying, containerRef.current])

  // Carregar o progresso de visualização
  const loadWatchProgress = async () => {
    if (!session?.user || !episodeId || !videoRef.current) return

    try {
      console.log('Carregando progresso para o episódio:', episodeId)
      const response = await fetch(`/api/watch-progress?episodeId=${episodeId}`)

      if (response.ok) {
        const data = await response.json()
        console.log('Progresso recebido:', data)

        if (data.currentTime && videoRef.current) {
          // Só definir o tempo se for maior que 0 e menor que 98% da duração
          if (data.currentTime > 0 && (!data.duration || data.percentage < 98)) {
            console.log('Definindo tempo do vídeo para:', data.currentTime)

            // Armazenar o tempo para aplicar após o vídeo estar pronto
            const savedTime = data.currentTime

            // Função para aplicar o tempo salvo
            const applyTime = () => {
              if (videoRef.current) {
                console.log('Aplicando tempo salvo:', savedTime)
                videoRef.current.currentTime = savedTime
                setCurrentTime(savedTime)
              }
            }

            // Aplicar imediatamente
            applyTime()

            // E também aplicar após um pequeno atraso para garantir
            setTimeout(applyTime, 1000)

            // Salvar no localStorage para recuperar após refresh
            try {
              localStorage.setItem(`video_progress_${episodeId}`, String(savedTime))
            } catch (e) {
              console.error('Erro ao salvar progresso no localStorage:', e)
            }
          }
        }
      }
    } catch (error) {
      console.error('Erro ao carregar progresso:', error)
    }
  }

  // Salvar o progresso de visualização
  const saveWatchProgress = async (currentTime: number, duration: number) => {
    if (!episodeId) return

    // Sempre salvar no localStorage, mesmo sem sessão
    try {
      if (currentTime > 0) {
        localStorage.setItem(`video_progress_${episodeId}`, String(currentTime))
        console.log('Progresso salvo no localStorage:', currentTime)
      }
    } catch (e) {
      console.error('Erro ao salvar progresso no localStorage:', e)
    }

    // Salvar no servidor apenas se o usuário estiver logado
    if (!session?.user) return

    try {
      await fetch('/api/watch-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          episodeId,
          currentTime,
          duration,
        }),
      })
      console.log('Progresso salvo no servidor:', currentTime)
    } catch (error) {
      console.error('Erro ao salvar progresso no servidor:', error)
    }
  }

  // Atualizar o status de assistido
  const updateWatchedStatus = async (episodeId: string, watched: boolean) => {
    if (!animeSlug || !session?.user) return

    try {
      const response = await fetch(`/api/animes/${animeSlug}/watched-episodes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          episodeId,
          watched,
        }),
      })

      if (!response.ok) {
        throw new Error('Falha ao atualizar status de assistido')
      }
    } catch (error) {
      console.error('Erro ao atualizar status:', error)
    }
  }

  // Funções de controle do player
  const togglePlay = () => {
    if (!videoRef.current) return

    if (isPlaying) {
      videoRef.current.pause()
    } else {
      videoRef.current.play()
    }
  }

  const toggleMute = () => {
    if (!videoRef.current) return

    videoRef.current.muted = !isMuted
  }

  const handleVolumeChange = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!volumeRef.current || !videoRef.current) return

    const rect = volumeRef.current.getBoundingClientRect()
    const volumeLevel = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width))

    videoRef.current.volume = volumeLevel
    setVolume(volumeLevel)
  }

  const handleProgressChange = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!progressRef.current || !videoRef.current) return

    const rect = progressRef.current.getBoundingClientRect()
    const seekTime = Math.max(0, Math.min(duration, duration * ((e.clientX - rect.left) / rect.width)))

    videoRef.current.currentTime = seekTime
    setCurrentTime(seekTime)
  }

  const toggleFullscreen = () => {
    if (!containerRef.current) return

    if (!isFullscreen) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen()
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      }
    }
  }

  // Detectar mudanças no estado de tela cheia
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
    }
  }, [])

  // Salvar o progresso quando o usuário sai da página
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (videoRef.current && episodeId) {
        const currentTime = videoRef.current.currentTime
        const duration = videoRef.current.duration

        // Salvar no localStorage antes de sair
        try {
          if (currentTime > 0) {
            localStorage.setItem(`video_progress_${episodeId}`, String(currentTime))
            console.log('Progresso salvo no localStorage antes de sair:', currentTime)
          }
        } catch (e) {
          console.error('Erro ao salvar progresso no localStorage antes de sair:', e)
        }

        // Tentar salvar no servidor de forma síncrona
        if (session?.user && currentTime > 0 && duration > 0) {
          const xhr = new XMLHttpRequest()
          xhr.open('POST', '/api/watch-progress', false) // síncrono
          xhr.setRequestHeader('Content-Type', 'application/json')
          xhr.send(JSON.stringify({
            episodeId,
            currentTime,
            duration
          }))
        }
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [videoRef.current, episodeId, session])

  // Formatar tempo (segundos para MM:SS)
  const formatTime = (timeInSeconds: number) => {
    const minutes = Math.floor(timeInSeconds / 60)
    const seconds = Math.floor(timeInSeconds % 60)
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`
  }

  return (
    <div
      ref={containerRef}
      className={`relative aspect-video bg-black overflow-hidden ${className} ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}
      onDoubleClick={toggleFullscreen}
    >
      {/* Vídeo */}
      <video
        ref={videoRef}
        className="w-full h-full"
        poster={poster}
        playsInline
        preload="auto"
        src={videoUrl}
        autoPlay={false}
        muted={isMuted}
        volume={volume}
        onClick={(e) => {
          // Garantir que o loading seja removido ao clicar no vídeo
          setIsLoading(false);
          togglePlay();
        }}
        onLoadStart={() => setIsLoading(false)}
        onLoadedMetadata={() => setIsLoading(false)}
        onCanPlayThrough={() => setIsLoading(false)}
      >
        <source src={videoUrl} type="video/mp4" />
        Seu navegador não suporta o elemento de vídeo.
      </video>

      {/* Removido completamente o spinner de carregamento para evitar problemas */}

      {/* Botão de play grande no centro - sempre visível quando o vídeo está pausado */}
      {!isPlaying && (
        <button
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 bg-purple-600/80 rounded-full flex items-center justify-center z-10 hover:bg-purple-600 transition-colors"
          onClick={() => {
            // Garantir que o loading seja removido ao clicar no botão de play
            setIsLoading(false);
            togglePlay();
          }}
        >
          <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M8 5v14l11-7z" />
          </svg>
        </button>
      )}

      {/* Controles */}
      <div
        className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 transition-opacity duration-300 ${showControls ? 'opacity-100' : 'opacity-0'}`}
      >
        {/* Barra de progresso */}
        <div
          ref={progressRef}
          className="w-full h-1.5 bg-gray-600 rounded-full mb-4 cursor-pointer"
          onClick={handleProgressChange}
        >
          <div
            className="h-full bg-purple-600 rounded-full relative"
            style={{ width: `${(currentTime / duration) * 100}%` }}
          >
            <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-purple-500 rounded-full"></div>
          </div>
        </div>

        {/* Controles inferiores */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Botão play/pause */}
            <button
              className="text-white hover:text-purple-400 transition-colors"
              onClick={togglePlay}
            >
              {isPlaying ? (
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" />
                </svg>
              ) : (
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z" />
                </svg>
              )}
            </button>

            {/* Controle de volume */}
            <div className="flex items-center space-x-2">
              <button
                className="text-white hover:text-purple-400 transition-colors"
                onClick={toggleMute}
              >
                {isMuted || volume === 0 ? (
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z" />
                  </svg>
                ) : volume < 0.5 ? (
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M18.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM5 9v6h4l5 5V4L9 9H5z" />
                  </svg>
                ) : (
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z" />
                  </svg>
                )}
              </button>

              <div
                ref={volumeRef}
                className="w-16 h-1.5 bg-gray-600 rounded-full cursor-pointer hidden md:block"
                onClick={handleVolumeChange}
              >
                <div
                  className="h-full bg-purple-600 rounded-full"
                  style={{ width: `${isMuted ? 0 : volume * 100}%` }}
                ></div>
              </div>
            </div>

            {/* Tempo */}
            <div className="text-white text-sm hidden sm:block">
              {formatTime(currentTime)} / {formatTime(duration)}
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* Botão de próximo episódio */}
            {onNextEpisode && (
              <button
                className="text-white hover:text-purple-400 transition-colors hidden sm:block"
                onClick={onNextEpisode}
                title="Próximo episódio"
              >
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z" />
                </svg>
              </button>
            )}

            {/* Botão de tela cheia */}
            <button
              className="text-white hover:text-purple-400 transition-colors"
              onClick={toggleFullscreen}
            >
              {isFullscreen ? (
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z" />
                </svg>
              ) : (
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
