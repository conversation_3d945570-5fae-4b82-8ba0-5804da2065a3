'use client'

import { useEffect, useState } from 'react'
import { monetagAds } from '@/config/ads'

export default function MontagScript() {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)

    // Verifica se o script já existe para evitar duplicação
    const existingScript = document.querySelector('script[data-monetag="true"]')
    if (!existingScript) {
      try {
        // Cria e adiciona o script da Monetag
        const script = document.createElement('script')
        script.setAttribute('data-monetag', 'true')
        script.innerHTML = `(function(d,z,s){s.src='https://'+d+'/400/'+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})('${monetagAds.globalScript.domain}',${monetagAds.globalScript.zoneId},document.createElement('script'))`
        document.body.appendChild(script)

        console.log('Script Monetag adicionado globalmente')
      } catch (error) {
        console.error('Erro ao adicionar script Monetag:', error)
      }
    }
  }, [])

  // Este componente não renderiza nada visível
  return null
}
