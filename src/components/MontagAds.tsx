'use client'

import { useEffect } from 'react'
import { monetagAds } from '@/config/ads'

interface MontagAdsProps {
  type: 'popunder' | 'vignette' | 'directLink'
}

export function MontagAds({ type }: MontagAdsProps) {
  useEffect(() => {
    if (type === 'popunder') {
      const script = document.createElement('script')
      script.src = monetagAds.popunder.scriptUrl
      script.setAttribute('data-zone', monetagAds.popunder.zoneId.toString())
      document.body.appendChild(script)
    }

    if (type === 'vignette') {
      const script = document.createElement('script')
      script.src = `https://${monetagAds.vignette.domain}/401/${monetagAds.vignette.zoneId}`
      document.body.appendChild(script)
    }

    // Direct link doesn't need client-side script
  }, [type])

  // Only render something visible for direct link
  if (type === 'directLink') {
    return (
      <a 
        href={monetagAds.directLink}
        target="_blank"
        rel="noopener noreferrer"
        className="hidden" // Hidden by default, style as needed
      >
        Advertisement
      </a>
    )
  }

  // Popunder and Vignette don't need to render anything visible
  return null
}
