'use client'

import { useEffect } from 'react'
import { monetagAds } from '@/config/ads'

interface MontagAdsProps {
  type: 'popunder' | 'vignette' | 'directLink'
}

export function MontagAds({ type }: MontagAdsProps) {
  useEffect(() => {
    // Popunder removido para evitar comportamento de abrir novas guias
    // if (type === 'popunder') {
    //   const script = document.createElement('script')
    //   script.src = monetagAds.popunder.scriptUrl
    //   script.setAttribute('data-zone', monetagAds.popunder.zoneId.toString())
    //   document.body.appendChild(script)
    // }

    // Vignette removido para evitar comportamento de abrir novas guias
    // if (type === 'vignette') {
    //   const script = document.createElement('script')
    //   script.src = `https://${monetagAds.vignette.domain}/401/${monetagAds.vignette.zoneId}`
    //   document.body.appendChild(script)
    // }

    // Direct link removido para evitar comportamento de abrir novas guias
    // if (type === 'directLink') {
    //   const script = document.createElement('script')
    //   script.innerHTML = `(function(s,u,z,p){s.src=u,s.setAttribute('data-zone',z),p.appendChild(s);})(document.createElement('script'),'https://al5sm.com/tag.min.js',9371544,document.body||document.documentElement)`
    //   document.body.appendChild(script)
    // }
  }, [type])

  // Direct link não precisa renderizar nada visível
  if (type === 'directLink') {
    return null
  }

  // Popunder and Vignette don't need to render anything visible
  return null
}
