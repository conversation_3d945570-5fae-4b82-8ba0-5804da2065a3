'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { ReloadIcon, CheckIcon, CrossCircledIcon } from '@radix-ui/react-icons'

export default function GenerateSitemap() {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<{
    success: boolean
    message: string
  } | null>(null)

  const handleGenerateSitemap = async () => {
    try {
      setIsLoading(true)
      setResult(null)

      const response = await fetch('/api/sitemap/generate')
      const data = await response.json()

      if (response.ok) {
        setResult({
          success: true,
          message: data.message
        })
      } else {
        setResult({
          success: false,
          message: data.error || 'Erro ao gerar sitemap'
        })
      }
    } catch (error) {
      setResult({
        success: false,
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Sitemap.xml</CardTitle>
        <CardDescription>
          Gere o sitemap.xml do site para melhorar a indexação nos motores de busca
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
          O sitemap.xml é atualizado automaticamente quando novos animes ou episódios são adicionados.
          Use esta opção apenas se precisar gerar o sitemap manualmente.
        </p>

        {result && (
          <Alert className={result.success ? 'bg-green-50 dark:bg-green-950' : 'bg-red-50 dark:bg-red-950'}>
            {result.success ? (
              <CheckIcon className="h-4 w-4 text-green-600 dark:text-green-400" />
            ) : (
              <CrossCircledIcon className="h-4 w-4 text-red-600 dark:text-red-400" />
            )}
            <AlertTitle>{result.success ? 'Sucesso!' : 'Erro!'}</AlertTitle>
            <AlertDescription>{result.message}</AlertDescription>
          </Alert>
        )}
      </CardContent>
      <CardFooter>
        <Button 
          onClick={handleGenerateSitemap} 
          disabled={isLoading}
          className="w-full"
        >
          {isLoading ? (
            <>
              <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
              Gerando sitemap...
            </>
          ) : (
            'Gerar Sitemap'
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}
