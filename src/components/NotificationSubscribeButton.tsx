'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'react-hot-toast'
import { Bell, BellOff } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface NotificationSubscribeButtonProps {
  animeId: string
  animeTitle: string
  animeSlug: string
  isOngoing: boolean
}

export default function NotificationSubscribeButton({
  animeId,
  animeTitle,
  animeSlug,
  isOngoing
}: NotificationSubscribeButtonProps) {
  const { data: session } = useSession()
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [permissionState, setPermissionState] = useState<NotificationPermission | 'unsupported'>('default')

  // Verificar se as notificações são suportadas
  useEffect(() => {
    if (!('Notification' in window)) {
      setPermissionState('unsupported')
      setIsLoading(false)
      return
    }

    setPermissionState(Notification.permission)
    
    // Verificar se o usuário já está inscrito neste anime
    if (session?.user) {
      checkSubscriptionStatus()
    } else {
      setIsLoading(false)
    }
  }, [session, animeId])

  // Verificar o status da inscrição
  const checkSubscriptionStatus = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/notifications/subscription-status?animeId=${animeId}`)
      
      if (response.ok) {
        const data = await response.json()
        setIsSubscribed(data.isSubscribed)
      }
    } catch (error) {
      console.error('Erro ao verificar status da inscrição:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Solicitar permissão para notificações
  const requestNotificationPermission = async () => {
    if (!('Notification' in window)) {
      toast.error('Seu navegador não suporta notificações push')
      return false
    }

    try {
      const permission = await Notification.requestPermission()
      setPermissionState(permission)
      return permission === 'granted'
    } catch (error) {
      console.error('Erro ao solicitar permissão:', error)
      return false
    }
  }

  // Inscrever-se para receber notificações
  const subscribeToNotifications = async () => {
    if (!session?.user) {
      toast.error('Você precisa estar logado para receber notificações')
      return
    }

    setIsLoading(true)

    try {
      // Primeiro, verificar/solicitar permissão
      const hasPermission = permissionState === 'granted' || await requestNotificationPermission()
      
      if (!hasPermission) {
        toast.error('Permissão para notificações negada')
        setIsLoading(false)
        return
      }

      // Registrar o service worker se ainda não estiver registrado
      const registration = await navigator.serviceWorker.ready

      // Obter a subscription atual ou criar uma nova
      let subscription = await registration.pushManager.getSubscription()
      
      if (!subscription) {
        // Obter as chaves VAPID do servidor
        const vapidResponse = await fetch('/api/notifications/vapid-public-key')
        const vapidData = await vapidResponse.json()
        
        // Criar nova subscription
        subscription = await registration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: vapidData.publicKey
        })
      }

      // Enviar a subscription para o servidor junto com o ID do anime
      const response = await fetch('/api/notifications/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subscription,
          animeId
        })
      })

      if (response.ok) {
        setIsSubscribed(true)
        toast.success(`Você receberá notificações de novos episódios de ${animeTitle}`)
      } else {
        const error = await response.json()
        throw new Error(error.message || 'Falha ao se inscrever para notificações')
      }
    } catch (error) {
      console.error('Erro ao se inscrever:', error)
      toast.error('Não foi possível ativar as notificações. Tente novamente.')
    } finally {
      setIsLoading(false)
    }
  }

  // Cancelar inscrição
  const unsubscribeFromNotifications = async () => {
    if (!session?.user) return

    setIsLoading(true)

    try {
      const response = await fetch('/api/notifications/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          animeId
        })
      })

      if (response.ok) {
        setIsSubscribed(false)
        toast.success(`Notificações de ${animeTitle} desativadas`)
      } else {
        const error = await response.json()
        throw new Error(error.message || 'Falha ao cancelar inscrição')
      }
    } catch (error) {
      console.error('Erro ao cancelar inscrição:', error)
      toast.error('Não foi possível desativar as notificações. Tente novamente.')
    } finally {
      setIsLoading(false)
    }
  }

  // Se o anime não estiver em lançamento, não mostrar o botão
  if (!isOngoing) {
    return null
  }

  // Se notificações não forem suportadas, não mostrar o botão
  if (permissionState === 'unsupported') {
    return null
  }

  return (
    <Button
      variant={isSubscribed ? "outline" : "default"}
      size="sm"
      className={`flex items-center gap-1 ${isSubscribed ? 'bg-gray-700 hover:bg-gray-600' : 'bg-purple-600 hover:bg-purple-700'}`}
      onClick={isSubscribed ? unsubscribeFromNotifications : subscribeToNotifications}
      disabled={isLoading}
    >
      {isSubscribed ? (
        <>
          <BellOff size={16} />
          <span className="ml-1">Cancelar notificações</span>
        </>
      ) : (
        <>
          <Bell size={16} />
          <span className="ml-1">Receber notificações</span>
        </>
      )}
    </Button>
  )
}
