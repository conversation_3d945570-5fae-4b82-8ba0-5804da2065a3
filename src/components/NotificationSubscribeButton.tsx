'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'react-hot-toast'
import { Bell, BellOff } from 'lucide-react'

// Função para converter a chave VAPID de base64 para Uint8Array
function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - (base64String.length % 4)) % 4)
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/')

  const rawData = window.atob(base64)
  const outputArray = new Uint8Array(rawData.length)

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i)
  }

  return outputArray
}

interface NotificationSubscribeButtonProps {
  animeId: string
  animeTitle: string
  animeSlug: string
  isOngoing: boolean
}

export default function NotificationSubscribeButton({
  animeId,
  animeTitle,
  animeSlug,
  isOngoing
}: NotificationSubscribeButtonProps) {
  const { data: session } = useSession()
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [permissionState, setPermissionState] = useState<NotificationPermission | 'unsupported'>('default')

  // Verificar se as notificações são suportadas
  useEffect(() => {
    if (!('Notification' in window)) {
      setPermissionState('unsupported')
      setIsLoading(false)
      return
    }

    setPermissionState(Notification.permission)

    // Verificar se o usuário já está inscrito neste anime
    if (session?.user) {
      checkSubscriptionStatus()
    } else {
      setIsLoading(false)
    }
  }, [session, animeId])

  // Verificar o status da inscrição
  const checkSubscriptionStatus = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/notifications/subscription-status?animeId=${animeId}`)

      if (response.ok) {
        const data = await response.json()
        setIsSubscribed(data.isSubscribed)
      }
    } catch (error) {
      console.error('Erro ao verificar status da inscrição:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Solicitar permissão para notificações
  const requestNotificationPermission = async () => {
    if (!('Notification' in window)) {
      toast.error('Seu navegador não suporta notificações push')
      return false
    }

    try {
      const permission = await Notification.requestPermission()
      setPermissionState(permission)
      return permission === 'granted'
    } catch (error) {
      console.error('Erro ao solicitar permissão:', error)
      return false
    }
  }

  // Inscrever-se para receber notificações
  const subscribeToNotifications = async () => {
    if (!session?.user) {
      toast.error('Você precisa estar logado para receber notificações')
      return
    }

    setIsLoading(true)

    try {
      // Primeiro, verificar/solicitar permissão
      const hasPermission = permissionState === 'granted' || await requestNotificationPermission()

      if (!hasPermission) {
        toast.error('Permissão para notificações negada')
        setIsLoading(false)
        return
      }

      // Verificar se o service worker está registrado
      if (!('serviceWorker' in navigator)) {
        toast.error('Seu navegador não suporta service workers')
        setIsLoading(false)
        return
      }

      // Registrar o service worker se ainda não estiver registrado
      let swRegistration
      try {
        // Verificar se já existe um service worker registrado
        const existingRegistration = await navigator.serviceWorker.getRegistration('/notification-sw.js')

        if (existingRegistration) {
          swRegistration = existingRegistration
        } else {
          // Registrar novo service worker
          swRegistration = await navigator.serviceWorker.register('/notification-sw.js', {
            scope: '/'
          })
          console.log('Notification Service Worker registrado com sucesso:', swRegistration.scope)
        }
      } catch (error) {
        console.error('Erro ao registrar service worker:', error)
        toast.error('Erro ao configurar notificações')
        setIsLoading(false)
        return
      }

      // Aguardar até que o service worker esteja ativo
      await navigator.serviceWorker.ready

      // Obter a subscription atual ou criar uma nova
      let subscription
      try {
        subscription = await swRegistration.pushManager.getSubscription()

        if (!subscription) {
          // Obter as chaves VAPID do servidor
          const vapidResponse = await fetch('/api/notifications/vapid-public-key')

          if (!vapidResponse.ok) {
            throw new Error('Erro ao obter chaves de notificação')
          }

          const vapidData = await vapidResponse.json()

          // Converter a chave pública para o formato correto
          const applicationServerKey = urlBase64ToUint8Array(vapidData.publicKey)

          // Criar nova subscription
          subscription = await swRegistration.pushManager.subscribe({
            userVisibleOnly: true,
            applicationServerKey
          })
        }
      } catch (error) {
        console.error('Erro ao criar subscription:', error)
        toast.error('Erro ao configurar notificações')
        setIsLoading(false)
        return
      }

      // Enviar a subscription para o servidor junto com o ID do anime
      const response = await fetch('/api/notifications/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subscription,
          animeId
        })
      })

      if (response.ok) {
        setIsSubscribed(true)
        toast.success(`Você receberá notificações de novos episódios de ${animeTitle}`)
      } else {
        const error = await response.json()
        throw new Error(error.message || 'Falha ao se inscrever para notificações')
      }
    } catch (error) {
      console.error('Erro ao se inscrever:', error)
      toast.error('Não foi possível ativar as notificações. Tente novamente.')
    } finally {
      setIsLoading(false)
    }
  }

  // Cancelar inscrição
  const unsubscribeFromNotifications = async () => {
    if (!session?.user) return

    setIsLoading(true)

    try {
      const response = await fetch('/api/notifications/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          animeId
        })
      })

      if (response.ok) {
        setIsSubscribed(false)
        toast.success(`Notificações de ${animeTitle} desativadas`)
      } else {
        const error = await response.json()
        throw new Error(error.message || 'Falha ao cancelar inscrição')
      }
    } catch (error) {
      console.error('Erro ao cancelar inscrição:', error)
      toast.error('Não foi possível desativar as notificações. Tente novamente.')
    } finally {
      setIsLoading(false)
    }
  }

  // Se o anime não estiver em lançamento, não mostrar o botão
  if (!isOngoing) {
    return null
  }

  // Se notificações não forem suportadas, não mostrar o botão
  if (permissionState === 'unsupported') {
    return null
  }

  return (
    <button
      className={`flex items-center justify-center gap-1 rounded-md px-3 py-2 text-sm font-medium transition-colors w-full sm:w-auto ${
        isLoading ? 'opacity-70 cursor-not-allowed' : ''
      } ${
        isSubscribed
          ? 'bg-gray-700 hover:bg-gray-600 text-white active:bg-gray-800'
          : 'bg-purple-600 hover:bg-purple-700 text-white active:bg-purple-800'
      }`}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        if (!isLoading) {
          isSubscribed ? unsubscribeFromNotifications() : subscribeToNotifications();
        }
      }}
      disabled={isLoading}
      type="button"
      aria-label={isSubscribed ? "Cancelar notificações" : "Receber notificações"}
    >
      {isLoading ? (
        <>
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
          <span className="ml-1 hidden sm:inline">Processando...</span>
        </>
      ) : isSubscribed ? (
        <>
          <BellOff size={16} className="flex-shrink-0" />
          <span className="ml-1 hidden xs:inline">Cancelar notificações</span>
          <span className="ml-1 xs:hidden">Cancelar</span>
        </>
      ) : (
        <>
          <Bell size={16} className="flex-shrink-0" />
          <span className="ml-1 hidden xs:inline">Receber notificações</span>
          <span className="ml-1 xs:hidden">Notificar</span>
        </>
      )}
    </button>
  )
}
