'use client'

import { useState, useEffect } from 'react'
import { monetagAds } from '@/config/ads'

interface AdBannerProps {
  position: 'top' | 'bottom' | 'sidebar' | 'in-content'
  className?: string
}

export default function AdBanner({ position, className = '' }: AdBannerProps) {
  const [isClient, setIsClient] = useState(false)
  const [adId] = useState(`ad-${Math.random().toString(36).substring(2, 9)}`)

  useEffect(() => {
    setIsClient(true)

    // Adiciona o script de anúncio Monetag
    const script = document.createElement('script')
    script.innerHTML = `(function(d,z,s){s.src='https://'+d+'/400/'+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})('${monetagAds.globalScript.domain}',${monetagAds.globalScript.zoneId},document.createElement('script'))`

    // Insere o script no container do anúncio
    const adContainer = document.getElementById(adId)
    if (adContainer) {
      // Limpa qualquer script anterior
      const scriptContainer = adContainer.querySelector('.ad-script-container')
      if (scriptContainer) {
        scriptContainer.innerHTML = ''
        scriptContainer.appendChild(script)
      }
    }

    // Cleanup ao desmontar o componente
    return () => {
      if (adContainer && script.parentNode) {
        script.parentNode.removeChild(script)
      }
    }
  }, [adId])

  if (!isClient) {
    return null
  }

  // Definir tamanhos e estilos com base na posição
  let adStyle = ''
  let adSize = ''

  switch (position) {
    case 'top':
      adStyle = 'w-full mx-auto my-4 overflow-hidden text-center'
      adSize = 'min-h-[90px] md:min-h-[90px]' // Leaderboard (728x90)
      break
    case 'bottom':
      adStyle = 'w-full mx-auto my-4 overflow-hidden text-center'
      adSize = 'min-h-[90px] md:min-h-[90px]' // Leaderboard (728x90)
      break
    case 'sidebar':
      adStyle = 'w-full mx-auto my-4 overflow-hidden text-center'
      adSize = 'min-h-[250px]' // Medium Rectangle (300x250)
      break
    case 'in-content':
      adStyle = 'w-full mx-auto my-6 overflow-hidden text-center'
      adSize = 'min-h-[250px] md:min-h-[90px]' // Responsivo
      break
    default:
      adStyle = 'w-full mx-auto my-4 overflow-hidden text-center'
      adSize = 'min-h-[250px]'
  }

  return (
    <div
      id={adId}
      className={`ad-container ${adStyle} ${adSize} ${className} bg-gray-800 rounded-lg relative`}
      data-ad-position={position}
    >
      {/* Container for ad script */}
      <div className="ad-script-container">
        {isClient && (
          <div dangerouslySetInnerHTML={{
            __html: `<script>(function(d,z,s){s.src='https://'+d+'/400/'+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})('${monetagAds.globalScript.domain}',${monetagAds.globalScript.zoneId},document.createElement('script'))</script>`
          }} />
        )}
      </div>
    </div>
  )
}
