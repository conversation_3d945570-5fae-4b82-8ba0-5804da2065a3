'use client'

import { useState, useEffect } from 'react'
import { monetagAds } from '@/config/ads'

interface AdBannerProps {
  position: 'top' | 'bottom' | 'sidebar' | 'in-content'
  className?: string
}

export default function AdBanner({ position, className = '' }: AdBannerProps) {
  const [isClient, setIsClient] = useState(false)
  const [adId] = useState(`ad-${Math.random().toString(36).substring(2, 9)}`)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Segundo useEffect para garantir que o DOM está pronto
  useEffect(() => {
    if (!isClient) return

    // Pequeno atraso para garantir que o DOM está completamente carregado
    const timer = setTimeout(() => {
      try {
        // Adicionar script diretamente no banner
        const adContainer = document.getElementById(adId)
        if (adContainer) {
          // Limpar qualquer conteúdo existente no container
          const scriptContainer = adContainer.querySelector('.ad-script-container')
          if (scriptContainer) {
            // Limpar o container primeiro
            scriptContainer.innerHTML = ''

            // Criar e adicionar o script
            const scriptElement = document.createElement('script')
            scriptElement.text = `
              (function(wqutvy){
                var d = document,
                    s = d.createElement('script'),
                    l = d.scripts[d.scripts.length - 1];
                s.settings = wqutvy || {};
                s.src = "//complete-drink.com/bLXYVPs/d.GRlv0sYSWfcp/Le/mK9ku/ZzUJlBkxP_TcYSzgNyzNUA1/MODDA/tUNGjnMK3/NQT/UrwfMAQd";
                s.async = true;
                s.referrerPolicy = 'no-referrer-when-downgrade';
                l.parentNode.insertBefore(s, l);
              })({})
            `
            scriptContainer.appendChild(scriptElement)

            console.log('Script de anúncio adicionado ao banner:', adId)
          }
        }
      } catch (error) {
        console.error('Erro ao adicionar script de anúncio:', error)
      }
    }, 500) // 500ms de atraso

    return () => clearTimeout(timer)
  }, [isClient, adId])

  if (!isClient) {
    return null
  }

  // Definir tamanhos e estilos com base na posição
  let adStyle = ''
  let adSize = ''

  switch (position) {
    case 'top':
      adStyle = 'w-full mx-auto my-4 overflow-hidden text-center'
      adSize = 'min-h-[250px]' // Alterado para 300x250
      break
    case 'bottom':
      adStyle = 'w-full mx-auto my-4 overflow-hidden text-center'
      adSize = 'min-h-[250px]' // Alterado para 300x250
      break
    case 'sidebar':
      adStyle = 'w-full mx-auto my-4 overflow-hidden text-center'
      adSize = 'min-h-[250px]' // Medium Rectangle (300x250)
      break
    case 'in-content':
      adStyle = 'w-full mx-auto my-6 overflow-hidden text-center'
      adSize = 'min-h-[250px]' // Alterado para 300x250
      break
    default:
      adStyle = 'w-full mx-auto my-4 overflow-hidden text-center'
      adSize = 'min-h-[250px]'
  }

  return (
    <div
      id={adId}
      className={`ad-container ${adStyle} ${adSize} ${className} bg-gray-800 rounded-lg relative`}
      data-ad-position={position}
      style={{ width: '300px', height: '250px', margin: '0 auto' }}
    >
      {/* Mensagem sobre anúncios */}
      <p className="text-xs text-gray-400 absolute top-0 left-0 right-0 text-center">
        Anúncios ajudam a manter o site
      </p>

      {/* Container for ad script */}
      <div className="ad-script-container" style={{ width: '300px', height: '250px' }}>
        {/* Fallback para garantir que o script seja carregado */}
        {isClient && (
          <div
            dangerouslySetInnerHTML={{
              __html: `
                <script>
                (function(wqutvy){
                  var d = document,
                      s = d.createElement('script'),
                      l = d.scripts[d.scripts.length - 1];
                  s.settings = wqutvy || {};
                  s.src = "//complete-drink.com/bLXYVPs/d.GRlv0sYSWfcp/Le/mK9ku/ZzUJlBkxP_TcYSzgNyzNUA1/MODDA/tUNGjnMK3/NQT/UrwfMAQd";
                  s.async = true;
                  s.referrerPolicy = 'no-referrer-when-downgrade';
                  l.parentNode.insertBefore(s, l);
                })({})
                </script>
              `
            }}
          />
        )}
      </div>
    </div>
  )
}
