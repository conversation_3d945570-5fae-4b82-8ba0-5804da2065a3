import { prisma } from '@/lib/prisma'
import fs from 'fs'
import path from 'path'

/**
 * Gera o sitemap.xml com base nos animes e episódios do banco de dados
 * e salva no diretório public
 */
export async function generateSitemap() {
  try {
    console.log('Iniciando geração do sitemap.xml...')
    
    // Buscar todos os animes do banco de dados com seus episódios
    const animes = await prisma.anime.findMany({
      select: {
        slug: true,
        updatedAt: true,
        episodes: {
          select: {
            number: true,
            updatedAt: true,
          },
          orderBy: {
            number: 'asc',
          },
        },
      },
    })
    
    console.log(`Encontrados ${animes.length} animes para incluir no sitemap`)
    
    // Criar o XML do sitemap
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
    xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n'
    
    // Adicionar páginas estáticas principais
    const staticPages = [
      { url: '', priority: '1.0', changefreq: 'daily' },
      { url: 'animes', priority: '0.9', changefreq: 'daily' },
      { url: 'favoritos', priority: '0.8', changefreq: 'weekly' },
      { url: 'continue-assistindo', priority: '0.8', changefreq: 'daily' },
      { url: 'login', priority: '0.7', changefreq: 'monthly' },
      { url: 'register', priority: '0.7', changefreq: 'monthly' },
      { url: 'privacidade', priority: '0.5', changefreq: 'yearly' },
      { url: 'dmca', priority: '0.5', changefreq: 'yearly' },
      { url: 'contato', priority: '0.6', changefreq: 'monthly' },
    ]
    
    // Adicionar as páginas estáticas ao sitemap
    staticPages.forEach(page => {
      xml += '  <url>\n'
      xml += `    <loc>https://animeszera.com.br/${page.url}</loc>\n`
      xml += `    <changefreq>${page.changefreq}</changefreq>\n`
      xml += `    <priority>${page.priority}</priority>\n`
      xml += '  </url>\n'
    })
    
    // Adicionar as páginas de animes ao sitemap
    animes.forEach(anime => {
      // Página principal do anime
      xml += '  <url>\n'
      xml += `    <loc>https://animeszera.com.br/animes/${anime.slug}</loc>\n`
      xml += `    <lastmod>${anime.updatedAt.toISOString().split('T')[0]}</lastmod>\n`
      xml += '    <changefreq>weekly</changefreq>\n'
      xml += '    <priority>0.8</priority>\n'
      xml += '  </url>\n'
      
      // Páginas de episódios individuais
      if (anime.episodes && anime.episodes.length > 0) {
        anime.episodes.forEach(episode => {
          xml += '  <url>\n'
          xml += `    <loc>https://animeszera.com.br/animes/${anime.slug}?episode=${episode.number}</loc>\n`
          xml += `    <lastmod>${episode.updatedAt.toISOString().split('T')[0]}</lastmod>\n`
          xml += '    <changefreq>monthly</changefreq>\n'
          xml += '    <priority>0.7</priority>\n'
          xml += '  </url>\n'
        })
      }
    })
    
    xml += '</urlset>'
    
    // Caminho para o arquivo sitemap.xml no diretório public
    const publicDir = path.join(process.cwd(), 'public')
    const sitemapPath = path.join(publicDir, 'sitemap.xml')
    
    // Salvar o arquivo
    fs.writeFileSync(sitemapPath, xml)
    
    console.log(`Sitemap.xml gerado com sucesso em ${sitemapPath}`)
    
    return {
      success: true,
      message: 'Sitemap.xml gerado com sucesso',
      path: sitemapPath
    }
  } catch (error) {
    console.error('Erro ao gerar sitemap:', error)
    return {
      success: false,
      message: `Erro ao gerar sitemap: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}
